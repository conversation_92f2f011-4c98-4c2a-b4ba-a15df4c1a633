# WebSocket断开重连功能实现说明

## 概述

本文档说明ESP32设备中WebSocket断开重连功能的实现方案，包括自动重连机制、WiFi重连后的WebSocket重连，以及相关的配置参数。

## 功能特点

### ✅ **已实现的重连机制**

1. **WebSocket主任务循环重连**
   - 在连接失败时自动重试
   - 支持手动触发重连
   - 5秒重连间隔

2. **WebSocket事件驱动重连**
   - 监听断开事件自动重连
   - 最大重连次数限制
   - 重连计数器管理

3. **WiFi重连后WebSocket重连**
   - WiFi连接成功后自动检查WebSocket状态
   - 自动启动WebSocket连接
   - 网络稳定性检查

## 实现细节

### 1. WebSocket主任务重连逻辑

<augment_code_snippet path="main/protocol/sk_websocket.c" mode="EXCERPT">
```c
void SkWsMainTask() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    uint32_t flagWaitConnect = SK_WS_EVENT_CONNECT_BIT | SK_WS_EVENT_STOP_BIT;
    uint32_t flagAtReconnect = SK_WS_EVENT_DISCONNECT_BIT | SK_WS_EVENT_STOP_BIT;
    uint32_t event = 0;

    while ((event & SK_WS_EVENT_STOP_BIT) == 0) {
        event = xEventGroupGetBits(ctrl->eventGroup);
        if ((event & SK_WS_EVENT_CONNECT_BIT) == 0) {
            event = xEventGroupWaitBits(ctrl->eventGroup, flagWaitConnect, pdFALSE, pdFALSE, pdMS_TO_TICKS(5000));
            continue;
        }
        if (SkWsConnect(ctrl) != SK_RET_SUCCESS) {
            // 连接失败时等待5秒后重试
            event = xEventGroupWaitBits(ctrl->eventGroup, flagAtReconnect, pdFALSE, pdFALSE, pdMS_TO_TICKS(5000));
            continue;
        }
        // 连接成功后进入接收循环
        SkWsRxLoop(ctrl);
        SkWsDisconnect(ctrl);
    }
}
```
</augment_code_snippet>

### 2. WebSocket事件处理回调

<augment_code_snippet path="main/app/main.c" mode="EXCERPT">
```c
void SkMainWebSocketEventHandler(void *arg, uint32_t event) {
    static uint32_t reconnectCount = 0;
    const uint32_t maxReconnectCount = 10;
    const uint32_t reconnectDelayMs = 5000; // 5秒重连间隔
    
    switch (event) {
        case SK_WS_EVENT_CONNECTED:
            SK_LOGI(TAG, "WebSocket connected successfully");
            reconnectCount = 0; // 重置重连计数
            break;
            
        case SK_WS_EVENT_DISCONNECTED:
            SK_LOGI(TAG, "WebSocket disconnected, attempting reconnect...");
            if (reconnectCount < maxReconnectCount) {
                reconnectCount++;
                SK_LOGI(TAG, "WebSocket reconnect attempt %d/%d", reconnectCount, maxReconnectCount);
                
                // 延迟后重连
                vTaskDelay(pdMS_TO_TICKS(reconnectDelayMs));
                SkWsStartConnect();
            } else {
                SK_LOGE(TAG, "WebSocket max reconnect attempts reached, giving up");
            }
            break;
    }
}
```
</augment_code_snippet>

### 3. WiFi重连后WebSocket重连

<augment_code_snippet path="main/app/main.c" mode="EXCERPT">
```c
void SkMainWifiEventHandler(uint32_t event) {
    // 先调用原有的状态机处理
    SkSmOnWifiEvent(event);
    
    // 添加WebSocket重连逻辑
    if (event == SK_WIFI_EVENT_STA_CONNECTED) {
        SK_LOGI(TAG, "WiFi connected, checking WebSocket connection...");
        
        // 延迟一段时间确保网络稳定
        vTaskDelay(pdMS_TO_TICKS(2000));
        
        // 如果WebSocket未连接，则启动连接
        if (!SkWsIsConnected()) {
            SK_LOGI(TAG, "WebSocket not connected, starting connection...");
            SkWsStartConnect();
        } else {
            SK_LOGI(TAG, "WebSocket already connected");
        }
    }
}
```
</augment_code_snippet>

## 重连参数配置

### WebSocket重连参数
- **最大重连次数**: 10次
- **重连间隔**: 5秒
- **网络稳定等待时间**: 2秒

### WiFi重连参数（已有）
- **最大重连次数**: 由`MAX_RECONNECT_COUNT`定义
- **重连间隔**: 立即重试，失败后等待10秒扫描

## 重连触发条件

### 1. 自动重连触发
- WebSocket连接断开事件
- WebSocket连接失败
- WiFi重连成功后

### 2. 手动重连触发
- 调用`SkWsStartConnect()`函数
- JSON命令`start_audio`
- 系统初始化后自动连接

## 重连状态管理

### 连接状态检查
```c
bool SkWsIsConnected() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    return ctrl->connected;
}
```

### 事件标志管理
- `SK_WS_EVENT_CONNECT_BIT`: 连接请求标志
- `SK_WS_EVENT_DISCONNECT_BIT`: 断开请求标志
- `SK_WS_EVENT_STOP_BIT`: 停止标志

## 日志输出

### 连接成功日志
```
I (12345) SmartKid: WebSocket connected successfully
```

### 重连日志
```
I (12346) SmartKid: WebSocket disconnected, attempting reconnect...
I (12347) SmartKid: WebSocket reconnect attempt 1/10
```

### WiFi重连后检查日志
```
I (12348) SmartKid: WiFi connected, checking WebSocket connection...
I (12349) SmartKid: WebSocket not connected, starting connection...
```

## 错误处理

### 重连失败处理
- 达到最大重连次数后停止重连
- 记录错误日志
- 保持系统其他功能正常运行

### 网络异常处理
- WiFi断开时WebSocket自动断开
- WiFi重连成功后WebSocket自动重连
- 网络不稳定时的重连保护

## 性能优化

### 1. 重连频率控制
- 避免频繁重连消耗资源
- 合理的重连间隔设置
- 最大重连次数限制

### 2. 内存管理
- 重连过程中的内存释放
- 避免内存泄漏
- 资源清理机制

### 3. 任务调度
- 重连任务优先级设置
- 避免阻塞其他重要任务
- 合理的延迟设置

## 测试验证

### 1. 断网重连测试
- 手动断开网络连接
- 观察自动重连行为
- 验证重连成功率

### 2. 服务器重启测试
- 重启WebSocket服务器
- 验证客户端自动重连
- 检查数据传输恢复

### 3. 长时间稳定性测试
- 24小时连续运行测试
- 网络波动环境测试
- 内存泄漏检查

## 故障排除

### 常见问题

#### 1. 重连失败
- 检查网络连接状态
- 确认服务器地址和端口
- 查看重连计数器状态

#### 2. 频繁重连
- 检查网络稳定性
- 调整重连间隔参数
- 检查服务器负载

#### 3. WiFi重连后WebSocket未重连
- 检查WiFi事件回调注册
- 确认网络稳定等待时间
- 查看相关日志输出

### 调试方法

#### 启用详细日志
```c
// 在sk_websocket.c中启用调试日志
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG
```

#### 监控重连状态
```c
// 添加状态监控代码
SK_LOGI(TAG, "WebSocket state: connected=%d, reconnectCount=%d", 
        SkWsIsConnected(), reconnectCount);
```

## 总结

本WebSocket重连功能实现了完整的自动重连机制，包括：

✅ **多层次重连保护**：主任务循环重连 + 事件驱动重连 + WiFi重连触发  
✅ **智能重连策略**：重连次数限制 + 合理间隔 + 状态检查  
✅ **完善的错误处理**：异常恢复 + 资源清理 + 日志记录  
✅ **性能优化**：频率控制 + 内存管理 + 任务调度  

该实现确保了WebSocket连接的高可用性和系统的稳定运行。
